/**
 * Enum for Comment Type
 */
export enum CommentType {
  NOTE = 'note',
  SYSTEM = 'system',
}

/**
 * Interface for Resource (attachments)
 */
export interface ResourceDto {
  type: string;
  url: string;
  name: string;
}

/**
 * Interface for Mention
 */
export interface MentionDto {
  userId: number;
  username: string;
}

/**
 * Interface for System Event Data
 */
export interface SystemEventData {
  eventType: string;
  oldValue?: any;
  newValue?: any;
  actorId: number;
  changedAt: number;
  [key: string]: any;
}

/**
 * Interface for Todo Comment Response (from backend)
 */
export interface TodoCommentResponseDto {
  id: number;
  todoId: number | null;
  userId: number | null;
  contentHtml: string;
  createdAt: number | null;
  parentId: number | null;
  commentType: CommentType | null;
  isSystemEvent: boolean;
  eventData: SystemEventData | null;
  resources: ResourceDto[] | null;
  mentions: MentionDto[] | null;
}

/**
 * Interface for Todo Comment Query
 */
export interface TodoCommentQueryDto {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  todoId?: number;
  userId?: number;
  parentId?: number;
  commentType?: CommentType;
  isSystemEvent?: boolean;
}

/**
 * Interface for Create Todo Comment
 */
export interface CreateTodoCommentDto {
  todoId: number;
  contentHtml: string;
  parentId?: number;
  commentType?: CommentType;
  resources?: ResourceDto[];
  mentions?: MentionDto[];
}

/**
 * Interface for Create System Event
 */
export interface CreateSystemEventDto {
  todoId: number;
  eventData: SystemEventData;
}

// Legacy interfaces for backward compatibility
/**
 * Interface for Comment (legacy)
 */
export interface CommentDto {
  id: number;
  taskId: number;
  userId: number;
  content: string;
  createdAt: number | null;
  updatedAt: number | null;
  userName?: string;
  userAvatar?: string;
}

/**
 * Interface for Comment Query (legacy)
 */
export interface CommentQueryDto {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  taskId?: number;
}

/**
 * Interface for Create Comment (legacy)
 */
export interface CreateCommentDto {
  taskId: number;
  content: string;
}

/**
 * Interface for Update Comment (legacy)
 */
export interface UpdateCommentDto {
  content: string;
}
