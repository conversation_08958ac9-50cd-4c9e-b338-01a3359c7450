import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { 
  Typography, 
  Chip, 
  IconCard, 
  Menu, 
  MenuItem,
  StarRating 
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';

import { TaskDto, TaskStatus, TaskPriority } from '../../types/task.types';

interface TaskDetailSidebarProps {
  task: TaskDto;
  onUpdateStatus: (status: TaskStatus) => void;
  onUpdatePriority: (priority: TaskPriority) => void;
  onUpdateAssignee: (assigneeId: string) => void;
  onUpdateExpectedStars: (stars: number) => void;
}

/**
 * Task detail sidebar component
 */
const TaskDetailSidebar: React.FC<TaskDetailSidebarProps> = ({
  task,
  onUpdateStatus,
  onUpdatePriority,
  onUpdateAssignee,
  onUpdateExpectedStars,
}) => {
  const { t } = useTranslation(['todolist']);
  const [statusMenuAnchor, setStatusMenuAnchor] = useState<HTMLElement | null>(null);
  const [priorityMenuAnchor, setPriorityMenuAnchor] = useState<HTMLElement | null>(null);
  const [assigneeMenuOpen, setAssigneeMenuOpen] = useState(false);

  // Get status badge color
  const getStatusBadgeColor = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-gray-100 text-gray-800';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case TaskStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return 'bg-green-100 text-green-800';
      case TaskPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800';
      case TaskPriority.HIGH:
        return 'bg-orange-100 text-orange-800';
      case TaskPriority.URGENT:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority text
  const getPriorityText = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return t('todolist:task.priority.low', 'Low');
      case TaskPriority.MEDIUM:
        return t('todolist:task.priority.medium', 'Medium');
      case TaskPriority.HIGH:
        return t('todolist:task.priority.high', 'High');
      case TaskPriority.URGENT:
        return t('todolist:task.priority.urgent', 'Urgent');
      default:
        return '';
    }
  };

  // Get status text
  const getStatusText = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return t('todolist:task.status.todo', 'To Do');
      case TaskStatus.IN_PROGRESS:
        return t('todolist:task.status.inProgress', 'In Progress');
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return t('todolist:task.status.done', 'Done');
      case TaskStatus.REJECTED:
        return t('todolist:task.status.cancelled', 'Cancelled');
      default:
        return '';
    }
  };

  // Load users for assignee selection
  const loadUsers = async ({ search, page = 1, limit = 20 }) => {
    // Mock data - replace with actual API call
    const mockUsers = [
      { value: '1', label: 'John Doe' },
      { value: '2', label: 'Jane Smith' },
      { value: '3', label: 'Bob Johnson' },
    ].filter(user => 
      !search || user.label.toLowerCase().includes(search.toLowerCase())
    );

    return {
      items: mockUsers,
      totalItems: mockUsers.length,
      totalPages: Math.ceil(mockUsers.length / limit),
      currentPage: page,
    };
  };

  return (
    <div className="p-4">
      <Typography variant="h6" className="mb-4">
        {t('todolist:task.details.title', 'Task Details')}
      </Typography>

      <div className="space-y-4">
        {/* Status */}
        <div>
          <Typography variant="subtitle2" className="text-gray-500 mb-1">
            {t('todolist:task.fields.status', 'Status')}
          </Typography>
          <div className="flex items-center space-x-2">
            <Chip className={getStatusBadgeColor(task.status)}>
              {getStatusText(task.status)}
            </Chip>
            <IconCard 
              icon="settings" 
              size="sm"
              onClick={(e) => setStatusMenuAnchor(e.currentTarget)}
              title={t('common:edit', 'Edit')}
            />
          </div>
        </div>

        {/* Priority */}
        <div>
          <Typography variant="subtitle2" className="text-gray-500 mb-1">
            {t('todolist:task.fields.priority', 'Priority')}
          </Typography>
          <div className="flex items-center space-x-2">
            <Chip className={getPriorityBadgeColor(task.priority)}>
              {getPriorityText(task.priority)}
            </Chip>
            <IconCard 
              icon="settings" 
              size="sm"
              onClick={(e) => setPriorityMenuAnchor(e.currentTarget)}
              title={t('common:edit', 'Edit')}
            />
          </div>
        </div>

        {/* Assignee */}
        <div>
          <Typography variant="subtitle2" className="text-gray-500 mb-1">
            {t('todolist:task.fields.assignee', 'Assignee')}
          </Typography>
          <div className="flex items-center space-x-2">
            <Typography variant="body2" className="flex-1">
              {task.assigneeId
                ? `${t('todolist:task.userIdLabel', 'User ID')}: ${task.assigneeId}`
                : t('todolist:task.noAssignee', 'No assignee')}
            </Typography>
            <IconCard 
              icon="settings" 
              size="sm"
              onClick={() => setAssigneeMenuOpen(true)}
              title={t('common:edit', 'Edit')}
            />
          </div>
        </div>

        {/* Expected Stars */}
        {task.expectedStars && (
          <div>
            <Typography variant="subtitle2" className="text-gray-500 mb-1">
              {t('todolist:task.fields.expectedStars', 'Expected Stars')}
            </Typography>
            <StarRating
              value={task.expectedStars}
              onChange={onUpdateExpectedStars}
              interactive={true}
              size="md"
            />
          </div>
        )}

        {/* Awarded Stars */}
        {task.awardedStars && (
          <div>
            <Typography variant="subtitle2" className="text-gray-500 mb-1">
              {t('todolist:task.fields.awardedStars', 'Awarded Stars')}
            </Typography>
            <StarRating
              value={task.awardedStars}
              interactive={false}
              size="md"
            />
          </div>
        )}
      </div>

      {/* Status Menu */}
      <Menu
        anchorEl={statusMenuAnchor}
        open={Boolean(statusMenuAnchor)}
        onClose={() => setStatusMenuAnchor(null)}
      >
        <MenuItem onClick={() => { onUpdateStatus(TaskStatus.PENDING); setStatusMenuAnchor(null); }}>
          {t('todolist:task.status.todo', 'To Do')}
        </MenuItem>
        <MenuItem onClick={() => { onUpdateStatus(TaskStatus.IN_PROGRESS); setStatusMenuAnchor(null); }}>
          {t('todolist:task.status.inProgress', 'In Progress')}
        </MenuItem>
        <MenuItem onClick={() => { onUpdateStatus(TaskStatus.COMPLETED); setStatusMenuAnchor(null); }}>
          {t('todolist:task.status.done', 'Done')}
        </MenuItem>
        <MenuItem onClick={() => { onUpdateStatus(TaskStatus.REJECTED); setStatusMenuAnchor(null); }}>
          {t('todolist:task.status.cancelled', 'Cancelled')}
        </MenuItem>
      </Menu>

      {/* Priority Menu */}
      <Menu
        anchorEl={priorityMenuAnchor}
        open={Boolean(priorityMenuAnchor)}
        onClose={() => setPriorityMenuAnchor(null)}
      >
        <MenuItem onClick={() => { onUpdatePriority(TaskPriority.LOW); setPriorityMenuAnchor(null); }}>
          {t('todolist:task.priority.low', 'Low')}
        </MenuItem>
        <MenuItem onClick={() => { onUpdatePriority(TaskPriority.MEDIUM); setPriorityMenuAnchor(null); }}>
          {t('todolist:task.priority.medium', 'Medium')}
        </MenuItem>
        <MenuItem onClick={() => { onUpdatePriority(TaskPriority.HIGH); setPriorityMenuAnchor(null); }}>
          {t('todolist:task.priority.high', 'High')}
        </MenuItem>
        <MenuItem onClick={() => { onUpdatePriority(TaskPriority.URGENT); setPriorityMenuAnchor(null); }}>
          {t('todolist:task.priority.urgent', 'Urgent')}
        </MenuItem>
      </Menu>

      {/* Assignee Select Modal */}
      {assigneeMenuOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <Typography variant="h6" className="mb-4">
              {t('todolist:task.selectAssignee', 'Select Assignee')}
            </Typography>
            <AsyncSelectWithPagination
              value={task.assigneeId?.toString()}
              onChange={(value) => {
                if (value) {
                  onUpdateAssignee(value.toString());
                }
                setAssigneeMenuOpen(false);
              }}
              loadOptions={loadUsers}
              placeholder={t('todolist:task.searchAssignee', 'Search for user...')}
              fullWidth
            />
            <div className="flex justify-end space-x-2 mt-4">
              <button
                onClick={() => setAssigneeMenuOpen(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                {t('common:cancel', 'Cancel')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskDetailSidebar;
