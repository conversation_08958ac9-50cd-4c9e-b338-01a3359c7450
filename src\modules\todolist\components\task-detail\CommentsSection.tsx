import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { 
  Typography, 
  ScrollArea,
  Avatar,
  Button,
  Textarea
} from '@/shared/components/common';
import { formatTimestamp } from '@/shared/utils/form-date-utils';

import { CommentDto } from '../../types/comment.types';

interface CommentsSectionProps {
  taskId: number;
  comments: CommentDto[];
  isLoading: boolean;
  onAddComment: (content: string) => void;
  onRefresh: () => void;
}

/**
 * Comments section component
 */
const CommentsSection: React.FC<CommentsSectionProps> = ({
  taskId,
  comments,
  isLoading,
  onAddComment,
  onRefresh,
}) => {
  const { t } = useTranslation(['todolist', 'common']);
  const [commentText, setCommentText] = useState('');

  const handleSendComment = () => {
    if (commentText.trim()) {
      onAddComment(commentText.trim());
      setCommentText('');
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages area */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full p-4">
          {comments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-8">
              <div className="text-4xl mb-4">💬</div>
              <Typography variant="h6" className="mb-2">
                {t('todolist:task.comments.empty', 'No comments yet')}
              </Typography>
              <Typography variant="body2" className="text-gray-500">
                {t('todolist:task.comments.emptyDescription', 'Be the first to comment!')}
              </Typography>
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment) => (
                <div key={comment.id} className="flex space-x-3">
                  <Avatar
                    src={comment.userAvatar}
                    alt={comment.userName || `User ${comment.userId}`}
                    size="sm"
                  />
                  <div className="flex-1">
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <Typography variant="subtitle2" className="font-medium">
                          {comment.userName || `User ${comment.userId}`}
                        </Typography>
                        <Typography variant="caption" className="text-gray-500">
                          {formatTimestamp(comment.createdAt || 0)}
                        </Typography>
                        {comment.updatedAt !== comment.createdAt && (
                          <Typography variant="caption" className="text-gray-500 italic">
                            ({t('todolist:task.comments.edited', 'edited')})
                          </Typography>
                        )}
                      </div>
                      <Typography variant="body2" className="whitespace-pre-wrap">
                        {comment.content}
                      </Typography>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Input area */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex space-x-2">
          <div className="flex-1">
            <Textarea
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              placeholder={t('todolist:task.comments.placeholder', 'Write a comment...')}
              rows={2}
              className="resize-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendComment();
                }
              }}
            />
          </div>
          <Button
            onClick={handleSendComment}
            disabled={!commentText.trim()}
            size="sm"
            variant="primary"
          >
            {t('todolist:task.comments.send', 'Send')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CommentsSection;
