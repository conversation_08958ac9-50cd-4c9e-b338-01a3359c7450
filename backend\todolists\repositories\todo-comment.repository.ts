import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TodoComment } from '../entities/todo-comment.entity';
import { TodoCommentQueryDto } from '../dto/todo-comment/todo-comment-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository xử lý truy vấn dữ liệu cho bình luận công việc
 */
@Injectable()
export class TodoCommentRepository {
  constructor(
    @InjectRepository(TodoComment)
    private readonly repository: Repository<TodoComment>,
  ) {}

  /**
   * Tạo mới bình luận
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu bình luận
   * @returns Bình luận đã tạo
   */
  async create(
    tenantId: number,
    data: Partial<TodoComment>,
  ): Promise<TodoComment> {
    const comment = this.repository.create({ ...data, tenantId });
    return this.repository.save(comment);
  }

  /**
   * Tìm bình luận theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID bình luận
   * @returns Bình luận nếu tìm thấy, null nếu không
   */
  async findById(tenantId: number, id: number): Promise<TodoComment | null> {
    return this.repository.findOne({ where: { id, tenantId } });
  }

  /**
   * Tìm tất cả bình luận với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách bình luận đã phân trang
   */
  async findAll(
    tenantId: number,
    query: TodoCommentQueryDto,
  ): Promise<PaginatedResult<TodoComment>> {
    const {
      page = 1,
      limit = 10,
      todoId,
      userId,
      parentId,
      commentType,
      isSystemEvent,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('comment');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('comment.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc todoId nếu được cung cấp
    if (todoId) {
      queryBuilder.andWhere('comment.todoId = :todoId', { todoId });
    }

    // Áp dụng bộ lọc userId nếu được cung cấp
    if (userId) {
      queryBuilder.andWhere('comment.userId = :userId', { userId });
    }

    // Áp dụng bộ lọc parentId nếu được cung cấp
    if (parentId !== undefined) {
      queryBuilder.andWhere('comment.parentId = :parentId', { parentId });
    }

    // Áp dụng bộ lọc commentType nếu được cung cấp
    if (commentType) {
      queryBuilder.andWhere('comment.commentType = :commentType', {
        commentType,
      });
    }

    // Áp dụng bộ lọc isSystemEvent nếu được cung cấp
    if (isSystemEvent !== undefined) {
      queryBuilder.andWhere('comment.isSystemEvent = :isSystemEvent', {
        isSystemEvent,
      });
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere('comment.contentHtml ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`comment.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm tất cả bình luận của một công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @returns Danh sách bình luận
   */
  async findByTodoId(tenantId: number, todoId: number): Promise<TodoComment[]> {
    return this.repository.find({
      where: { todoId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm tất cả bình luận con của một bình luận
   * @param tenantId ID tenant (required for tenant isolation)
   * @param parentId ID bình luận cha
   * @returns Danh sách bình luận con
   */
  async findReplies(
    tenantId: number,
    parentId: number,
  ): Promise<TodoComment[]> {
    return this.repository.find({
      where: { parentId, tenantId },
      order: { createdAt: 'ASC' },
    });
  }

  /**
   * Xóa bình luận
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID bình luận
   * @returns Kết quả xóa
   */
  async remove(tenantId: number, id: number): Promise<void> {
    await this.repository.delete({ id, tenantId });
  }
}
