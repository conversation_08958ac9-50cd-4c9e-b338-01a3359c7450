import { Injectable, Logger } from '@nestjs/common';
import { ProjectRepository } from '../repositories/project.repository';
import { ProjectMemberRepository } from '../repositories/project-member.repository';
import { Project } from '../entities/project.entity';
import { ProjectMember } from '../entities/project-members.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { CreateProjectDto } from '../dto/project/create-project.dto';
import { UpdateProjectDto } from '../dto/project/update-project.dto';
import { ProjectQueryDto } from '../dto/project/project-query.dto';
import { CreateProjectMemberDto } from '../dto/project-member/create-project-member.dto';
import { UpdateProjectMemberDto } from '../dto/project-member/update-project-member.dto';
import { QueryDto } from '@/common/dto/query.dto';
import { ProjectMemberRole } from '../enum/project-member-role.enum';

/**
 * Service xử lý logic nghiệp vụ cho dự án
 */
@Injectable()
export class ProjectService {
  private readonly logger = new Logger(ProjectService.name);

  constructor(
    private readonly projectRepository: ProjectRepository,
    private readonly projectMemberRepository: ProjectMemberRepository,
  ) {}

  /**
   * Tạo dự án mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng tạo dự án
   * @param createProjectDto Thông tin dự án
   * @returns Dự án đã tạo
   */
  async createProject(
    tenantId: number,
    userId: number,
    createProjectDto: CreateProjectDto,
  ): Promise<Project> {
    try {
      const now = Date.now();

      // Xác định người sở hữu dự án (mặc định là người tạo nếu không chỉ định)
      const ownerId = createProjectDto.ownerId || userId;

      // Tạo dự án mới
      const project = await this.projectRepository.create(tenantId, {
        title: createProjectDto.title,
        description: createProjectDto.description,
        ownerId: ownerId,
        createdBy: userId,
        createdAt: now,
        updatedAt: now,
        isActive: true,
      });

      // Thêm người sở hữu làm admin của dự án
      await this.projectMemberRepository.create(tenantId, {
        projectId: project.id,
        userId: ownerId,
        role: ProjectMemberRole.ADMIN,
        createdAt: now,
        updatedAt: now,
      });

      // Nếu người tạo khác với người sở hữu, thêm người tạo làm admin của dự án
      if (userId !== ownerId) {
        await this.projectMemberRepository.create(tenantId, {
          projectId: project.id,
          userId: userId,
          role: ProjectMemberRole.ADMIN,
          createdAt: now,
          updatedAt: now,
        });
      }

      return project;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo dự án: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.PROJECT_CREATION_FAILED,
        `Tạo dự án thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách dự án đã phân trang
   */
  async findAllProjects(
    tenantId: number,
    query: ProjectQueryDto,
  ): Promise<PaginatedResult<Project>> {
    try {
      return await this.projectRepository.findAll(tenantId, query);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách dự án: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.PROJECT_NOT_FOUND,
        `Lấy danh sách dự án thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID dự án
   * @returns Thông tin chi tiết dự án
   */
  async findProjectById(tenantId: number, id: number): Promise<Project> {
    const project = await this.projectRepository.findById(tenantId, id);
    if (!project) {
      throw new AppException(
        TODOLISTS_ERROR_CODES.PROJECT_NOT_FOUND,
        `Không tìm thấy dự án với ID ${id}`,
      );
    }
    return project;
  }

  /**
   * Cập nhật dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID dự án
   * @param userId ID người dùng cập nhật
   * @param updateProjectDto Thông tin cập nhật
   * @returns Dự án đã cập nhật
   */
  async updateProject(
    tenantId: number,
    id: number,
    userId: number,
    updateProjectDto: UpdateProjectDto,
  ): Promise<Project | null> {
    try {
      // Kiểm tra dự án tồn tại
      const project = await this.findProjectById(tenantId, id);

      // Kiểm tra quyền cập nhật (chỉ chủ sở hữu hoặc admin của dự án mới có quyền cập nhật)
      if (
        project.ownerId !== userId &&
        !(await this.projectMemberRepository.isProjectAdmin(
          tenantId,
          id,
          userId,
        ))
      ) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_PROJECT_OWNER,
          'Bạn không có quyền cập nhật dự án này',
        );
      }

      // Cập nhật dự án
      return await this.projectRepository.update(tenantId, id, {
        ...updateProjectDto,
        updatedAt: Date.now(),
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật dự án: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.PROJECT_UPDATE_FAILED,
        `Cập nhật dự án thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID dự án
   * @param userId ID người dùng xóa
   * @returns true nếu xóa thành công
   */
  async deleteProject(
    tenantId: number,
    id: number,
    userId: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra dự án tồn tại
      const project = await this.findProjectById(tenantId, id);

      // Kiểm tra quyền xóa (chỉ chủ sở hữu mới có quyền xóa)
      if (project.ownerId !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_PROJECT_OWNER,
          'Bạn không có quyền xóa dự án này',
        );
      }

      // Xóa dự án
      const result = await this.projectRepository.delete(tenantId, id);
      if (!result) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.PROJECT_DELETE_FAILED,
          `Xóa dự án thất bại`,
        );
      }
      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa dự án: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.PROJECT_DELETE_FAILED,
        `Xóa dự án thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Thêm thành viên vào dự án
   * @param projectId ID dự án
   * @param userId ID người dùng thêm thành viên
   * @param createProjectMemberDto Thông tin thành viên
   * @returns Thành viên đã thêm
   */
  async addProjectMember(
    tenantId: number,
    projectId: number,
    userId: number,
    createProjectMemberDto: CreateProjectMemberDto,
  ): Promise<ProjectMember> {
    try {
      // Kiểm tra dự án tồn tại
      const project = await this.findProjectById(tenantId, projectId);

      // Kiểm tra quyền thêm thành viên (chỉ chủ sở hữu hoặc admin của dự án mới có quyền thêm thành viên)
      if (
        project.ownerId !== userId &&
        !(await this.projectMemberRepository.isProjectAdmin(
          tenantId,
          projectId,
          userId,
        ))
      ) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_PROJECT_OWNER,
          'Bạn không có quyền thêm thành viên vào dự án này',
        );
      }

      // Kiểm tra thành viên đã tồn tại trong dự án chưa
      const existingMember =
        await this.projectMemberRepository.findByProjectIdAndUserId(
          tenantId,
          projectId,
          createProjectMemberDto.userId,
        );
      if (existingMember) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.PROJECT_MEMBER_ALREADY_EXISTS,
          'Người dùng đã là thành viên của dự án này',
        );
      }

      // Thêm thành viên vào dự án
      const now = Date.now();
      return await this.projectMemberRepository.create(tenantId, {
        projectId,
        userId: createProjectMemberDto.userId,
        role: createProjectMemberDto.role,
        createdAt: now,
        updatedAt: now,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi thêm thành viên vào dự án: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.PROJECT_MEMBER_ALREADY_EXISTS,
        `Thêm thành viên vào dự án thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách thành viên dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param userId ID người dùng xem danh sách
   * @param query Tham số truy vấn
   * @returns Danh sách thành viên dự án đã phân trang
   */
  async findProjectMembers(
    tenantId: number,
    projectId: number,
    userId: number,
    query: QueryDto,
  ): Promise<PaginatedResult<ProjectMember>> {
    try {
      // Kiểm tra dự án tồn tại
      await this.findProjectById(tenantId, projectId);

      // Kiểm tra người dùng có phải là thành viên của dự án không
      const isMember = await this.projectMemberRepository.isProjectMember(
        tenantId,
        projectId,
        userId,
      );
      if (!isMember) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_PROJECT_MEMBER,
          'Bạn không phải là thành viên của dự án này',
        );
      }

      // Lấy danh sách thành viên
      return await this.projectMemberRepository.findAllByProjectId(
        tenantId,
        projectId,
        query,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách thành viên dự án: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.PROJECT_MEMBER_NOT_FOUND,
        `Lấy danh sách thành viên dự án thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật vai trò thành viên dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param memberId ID thành viên
   * @param userId ID người dùng cập nhật
   * @param updateProjectMemberDto Thông tin cập nhật
   * @returns Thành viên đã cập nhật
   */
  async updateProjectMember(
    tenantId: number,
    projectId: number,
    memberId: number,
    userId: number,
    updateProjectMemberDto: UpdateProjectMemberDto,
  ): Promise<ProjectMember | null> {
    try {
      // Kiểm tra dự án tồn tại
      const project = await this.findProjectById(tenantId, projectId);

      // Kiểm tra quyền cập nhật (chỉ chủ sở hữu hoặc admin của dự án mới có quyền cập nhật thành viên)
      if (
        project.ownerId !== userId &&
        !(await this.projectMemberRepository.isProjectAdmin(
          tenantId,
          projectId,
          userId,
        ))
      ) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_PROJECT_OWNER,
          'Bạn không có quyền cập nhật thành viên dự án này',
        );
      }

      // Kiểm tra thành viên tồn tại
      const member = await this.projectMemberRepository.findById(
        tenantId,
        memberId,
      );
      if (!member || member.projectId !== projectId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.PROJECT_MEMBER_NOT_FOUND,
          'Không tìm thấy thành viên dự án',
        );
      }

      // Cập nhật vai trò thành viên
      return await this.projectMemberRepository.update(tenantId, memberId, {
        role: updateProjectMemberDto.role,
        updatedAt: Date.now(),
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật vai trò thành viên dự án: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.PROJECT_UPDATE_FAILED,
        `Cập nhật vai trò thành viên dự án thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa thành viên khỏi dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param memberId ID thành viên
   * @param userId ID người dùng xóa
   * @returns true nếu xóa thành công
   */
  async removeProjectMember(
    tenantId: number,
    projectId: number,
    memberId: number,
    userId: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra dự án tồn tại
      const project = await this.findProjectById(tenantId, projectId);

      // Kiểm tra quyền xóa (chỉ chủ sở hữu hoặc admin của dự án mới có quyền xóa thành viên)
      if (
        project.ownerId !== userId &&
        !(await this.projectMemberRepository.isProjectAdmin(
          tenantId,
          projectId,
          userId,
        ))
      ) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_PROJECT_OWNER,
          'Bạn không có quyền xóa thành viên khỏi dự án này',
        );
      }

      // Kiểm tra thành viên tồn tại
      const member = await this.projectMemberRepository.findById(
        tenantId,
        memberId,
      );
      if (!member || member.projectId !== projectId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.PROJECT_MEMBER_NOT_FOUND,
          'Không tìm thấy thành viên dự án',
        );
      }

      // Không cho phép xóa chủ sở hữu dự án
      if (member.userId === project.ownerId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.PROJECT_DELETE_FAILED,
          'Không thể xóa chủ sở hữu dự án',
        );
      }

      // Xóa thành viên
      const result = await this.projectMemberRepository.delete(
        tenantId,
        memberId,
      );
      if (!result) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.PROJECT_DELETE_FAILED,
          `Xóa thành viên dự án thất bại`,
        );
      }
      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi xóa thành viên dự án: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.PROJECT_DELETE_FAILED,
        `Xóa thành viên dự án thất bại: ${error.message}`,
      );
    }
  }
}
