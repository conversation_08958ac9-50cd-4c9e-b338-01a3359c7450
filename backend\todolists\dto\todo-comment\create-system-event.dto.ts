import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNotEmpty, IsObject, Min } from 'class-validator';
import { SystemEventType } from '../../enum/system-event-type.enum';
import { SystemEventData } from '../../interfaces/system-event-data.type';

/**
 * DTO cho tạo mới sự kiện hệ thống
 */
export class CreateSystemEventDto {
  /**
   * ID của công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID công việc không được để trống' })
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId: number;

  /**
   * <PERSON><PERSON><PERSON> sự kiện hệ thống
   * @example "status_changed"
   */
  @ApiProperty({
    description: '<PERSON>ại sự kiện hệ thống',
    enum: SystemEventType,
    example: SystemEventType.STATUS_CHANGED,
    required: true,
  })
  @IsNotEmpty({ message: 'Loại sự kiện không được để trống' })
  @IsEnum(SystemEventType, { message: 'Loại sự kiện không hợp lệ' })
  eventType: SystemEventType;

  /**
   * Dữ liệu chi tiết của sự kiện
   * @example { "oldValue": "pending", "newValue": "in_progress", "actorId": 1, "changedAt": 1625097600000 }
   */
  @ApiProperty({
    description: 'Dữ liệu chi tiết của sự kiện',
    example: {
      oldValue: 'pending',
      newValue: 'in_progress',
      actorId: 1,
      changedAt: 1625097600000,
    },
    required: true,
  })
  @IsNotEmpty({ message: 'Dữ liệu sự kiện không được để trống' })
  @IsObject({ message: 'Dữ liệu sự kiện phải là đối tượng' })
  eventData: SystemEventData;
}
