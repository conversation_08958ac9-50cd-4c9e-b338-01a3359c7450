import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing collaborators on todos
 */
@Entity('todo_collaborators')
export class TodoCollaborator {
  /**
   * Unique identifier for the collaborator record
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the todo
   */
  @Column({ name: 'todo_id', type: 'integer', nullable: true })
  todoId: number | null;

  /**
   * ID of the user who is collaborating
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
