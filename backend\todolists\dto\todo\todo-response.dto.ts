import { ApiProperty } from '@nestjs/swagger';
import { TodoStatus } from '../../enum/todo-status.enum';
import { TodoPriority } from '../../enum/todo-priority.enum';

/**
 * DTO cho phản hồi thông tin công việc
 */
export class TodoResponseDto {
  /**
   * ID công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID công việc',
    example: 1,
  })
  id: number;

  /**
   * Tiêu đề công việc
   * @example "Thiết kế giao diện người dùng"
   */
  @ApiProperty({
    description: 'Tiêu đề công việc',
    example: 'Thiết kế giao diện người dùng',
  })
  title: string;

  /**
   * Mô tả chi tiết công việc
   * @example "Thiết kế giao diện người dùng cho trang chủ và trang sản phẩm"
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết công việc',
    example: 'Thiết kế giao diện người dùng cho trang chủ và trang sản phẩm',
    nullable: true,
  })
  description: string | null;

  /**
   * ID của người được giao công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người được giao công việc',
    example: 1,
  })
  assigneeId: number;

  /**
   * Trạng thái công việc
   * @example "pending"
   */
  @ApiProperty({
    description: 'Trạng thái công việc',
    enum: TodoStatus,
    example: TodoStatus.PENDING,
    nullable: true,
  })
  status: TodoStatus | null;

  /**
   * Mức độ ưu tiên của công việc
   * @example "medium"
   */
  @ApiProperty({
    description: 'Mức độ ưu tiên của công việc',
    enum: TodoPriority,
    example: TodoPriority.MEDIUM,
    nullable: true,
  })
  priority: TodoPriority | null;

  /**
   * Số sao kỳ vọng (1-5)
   * @example 3
   */
  @ApiProperty({
    description: 'Số sao kỳ vọng (1-5)',
    example: 3,
    nullable: true,
  })
  expectedStars: number | null;

  /**
   * Số sao đánh giá thực tế (1-5)
   * @example 4
   */
  @ApiProperty({
    description: 'Số sao đánh giá thực tế (1-5)',
    example: 4,
    nullable: true,
  })
  awardedStars: number | null;

  /**
   * ID người tạo công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID người tạo công việc',
    example: 1,
  })
  createdBy: number;

  /**
   * Thời gian tạo công việc (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian tạo công việc (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Thời gian cập nhật công việc (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật công việc (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  updatedAt: number | null;

  /**
   * Thời gian hoàn thành công việc (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian hoàn thành công việc (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  completedAt: number | null;

  /**
   * ID của dự án
   * @example 1
   */
  @ApiProperty({
    description: 'ID của dự án',
    example: 1,
    nullable: true,
  })
  categoryId: number | null;

  /**
   * ID của công việc cha
   * @example 2
   */
  @ApiProperty({
    description: 'ID của công việc cha',
    example: 2,
    nullable: true,
  })
  parentId: number | null;
}
