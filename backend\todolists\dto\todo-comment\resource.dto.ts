import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho tài nguyên (ảnh, file) liên quan đến bình luận
 */
export class ResourceDto {
  /**
   * Loại tài nguyên (image, file, ...)
   * @example "image"
   */
  @ApiProperty({
    description: 'Loại tài nguyên (image, file, ...)',
    example: 'image',
    required: true,
  })
  @IsNotEmpty({ message: 'Loại tài nguyên không được để trống' })
  @IsString({ message: 'Loại tài nguyên phải là chuỗi' })
  @MaxLength(50, { message: 'Loại tài nguyên không được vượt quá 50 ký tự' })
  type: string;

  /**
   * URL của tài nguyên
   * @example "https://example.com/photo.jpg"
   */
  @ApiProperty({
    description: 'URL của tài nguyên',
    example: 'https://example.com/photo.jpg',
    required: true,
  })
  @IsNotEmpty({ message: 'URL không được để trống' })
  @IsString({ message: 'URL phải là chuỗi' })
  @MaxLength(500, { message: 'URL không được vượt quá 500 ký tự' })
  url: string;

  /**
   * Tên tài nguyên
   * @example "photo.jpg"
   */
  @ApiProperty({
    description: 'Tên tài nguyên',
    example: 'photo.jpg',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên tài nguyên phải là chuỗi' })
  @MaxLength(255, { message: 'Tên tài nguyên không được vượt quá 255 ký tự' })
  name?: string;
}
