import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  MaxLength,
  IsOptional,
  IsInt,
  Min,
} from 'class-validator';

/**
 * DTO cho tạo dự án mới
 */
export class CreateProjectDto {
  /**
   * Tiêu đề dự án
   * @example "Dự án phát triển website"
   */
  @ApiProperty({
    description: 'Tiêu đề dự án',
    example: 'Dự án phát triển website',
    required: true,
  })
  @IsNotEmpty({ message: 'Tiêu đề không được để trống' })
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  @MaxLength(255, { message: 'Tiêu đề không được vượt quá 255 ký tự' })
  title: string;

  /**
   * Mô tả dự án
   * @example "Dự án phát triển website cho công ty ABC"
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả dự án',
    example: 'Dự án phát triển website cho công ty ABC',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * ID của người sở hữu dự án (nếu khác với người tạo)
   * @example 2
   */
  @ApiProperty({
    description: 'ID của người sở hữu dự án (nếu khác với người tạo)',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'ID người sở hữu phải là số nguyên' })
  @Min(1, { message: 'ID người sở hữu phải lớn hơn 0' })
  ownerId?: number;
}
