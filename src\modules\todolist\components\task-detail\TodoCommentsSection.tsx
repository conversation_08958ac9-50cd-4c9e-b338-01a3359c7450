import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Typography } from '@/shared/components/common';
import { formatTimestamp } from '@/shared/utils/form-date-utils';

import { TodoCommentResponseDto } from '../../types/comment.types';

interface TodoCommentsSectionProps {
  taskId: number;
  comments: TodoCommentResponseDto[];
  isLoading: boolean;
  onAddComment: (contentHtml: string) => void;
  onRefresh: () => void;
}

/**
 * Todo comments section component for new API
 */
const TodoCommentsSection: React.FC<TodoCommentsSectionProps> = ({
  taskId,
  comments,
  isLoading,
  onAddComment,
  onRefresh,
}) => {
  const { t } = useTranslation(['todolist', 'common']);
  const [commentText, setCommentText] = useState('');

  const handleSendComment = () => {
    if (commentText.trim()) {
      // Convert plain text to HTML for now
      const htmlContent = `<p>${commentText.trim()}</p>`;
      onAddComment(htmlContent);
      setCommentText('');
    }
  };

  // Convert comment to ChatMessage format
  const convertToMessage = (comment: TodoCommentResponseDto) => {
    let content: React.ReactNode;

    if (comment.isSystemEvent && comment.eventData) {
      // System event content
      content = (
        <div className="text-blue-600 dark:text-blue-400 text-sm">
          <div className="font-medium">
            {t('todolist:task.comments.systemEvent', 'System Event')}: {comment.eventData.eventType}
          </div>
          {comment.eventData.oldValue && comment.eventData.newValue && (
            <div className="text-xs mt-1">
              {comment.eventData.oldValue} → {comment.eventData.newValue}
            </div>
          )}
        </div>
      );
    } else {
      // Regular comment content
      content = (
        <div>
          <div
            className="prose prose-sm dark:prose-invert max-w-none"
            dangerouslySetInnerHTML={{ __html: comment.contentHtml }}
          />
          {comment.resources && comment.resources.length > 0 && (
            <div className="mt-2 space-y-1">
              <div className="text-xs text-gray-500">
                {t('todolist:task.comments.attachments', 'Attachments')}:
              </div>
              {comment.resources.map((resource, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <span className="text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
                    {resource.type}
                  </span>
                  <a
                    href={resource.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    {resource.name}
                  </a>
                </div>
              ))}
            </div>
          )}
          {comment.mentions && comment.mentions.length > 0 && (
            <div className="mt-2">
              <div className="text-xs text-gray-500">
                {t('todolist:task.comments.mentions', 'Mentions')}:
              </div>
              <div className="flex flex-wrap gap-1 mt-1">
                {comment.mentions.map((mention, index) => (
                  <span
                    key={index}
                    className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded"
                  >
                    @{mention.username}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    }

    return {
      content,
      sender: comment.isSystemEvent ? ('ai' as const) : ('user' as const),
      timestamp: new Date(comment.createdAt || Date.now()),
    };
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages area */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          {comments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-8">
              <div className="text-4xl mb-4">💬</div>
              <Typography variant="h6" className="mb-2">
                {t('todolist:task.comments.empty', 'No comments yet')}
              </Typography>
              <Typography variant="body2" className="text-gray-500">
                {t('todolist:task.comments.emptyDescription', 'Be the first to comment!')}
              </Typography>
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map(comment => {
                const message = convertToMessage(comment);
                return (
                  <div key={comment.id} className="mb-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                          {comment.isSystemEvent ? 'S' : 'U'}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {comment.isSystemEvent
                              ? 'System'
                              : `User ${comment.userId || 'Unknown'}`}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatTimestamp(comment.createdAt || 0)}
                          </span>
                          {comment.commentType && (
                            <span className="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 rounded">
                              {comment.commentType}
                            </span>
                          )}
                        </div>
                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                          {message.content}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Input area */}
      <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
        <div className="p-4">
          <div className="flex items-end space-x-3">
            <div className="flex-1">
              <div className="relative">
                <textarea
                  value={commentText}
                  onChange={e => setCommentText(e.target.value)}
                  placeholder={t('todolist:task.comments.placeholder', 'Write a comment...')}
                  rows={1}
                  className="w-full resize-none border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  style={{ minHeight: '40px', maxHeight: '120px' }}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendComment();
                    }
                  }}
                  onInput={e => {
                    const target = e.target as HTMLTextAreaElement;
                    target.style.height = 'auto';
                    target.style.height = Math.min(target.scrollHeight, 120) + 'px';
                  }}
                />
              </div>
            </div>
            <button
              onClick={handleSendComment}
              disabled={!commentText.trim()}
              className="flex-shrink-0 w-10 h-10 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg flex items-center justify-center transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TodoCommentsSection;
