import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin dự án
 */
export class ProjectResponseDto {
  /**
   * ID dự án
   * @example 1
   */
  @ApiProperty({
    description: 'ID dự án',
    example: 1,
  })
  id: number;

  /**
   * Tiêu đề dự án
   * @example "Dự án phát triển website"
   */
  @ApiProperty({
    description: 'Tiêu đề dự án',
    example: 'Dự án phát triển website',
  })
  title: string;

  /**
   * Mô tả dự án
   * @example "Dự án phát triển website cho công ty ABC"
   */
  @ApiProperty({
    description: 'Mô tả dự án',
    example: 'Dự án phát triển website cho công ty ABC',
    nullable: true,
  })
  description: string | null;

  /**
   * ID chủ sở hữu dự án
   * @example 1
   */
  @ApiProperty({
    description: 'ID chủ sở hữu dự án',
    example: 1,
  })
  ownerId: number;

  /**
   * ID người tạo dự án
   * @example 1
   */
  @ApiProperty({
    description: 'ID người tạo dự án',
    example: 1,
  })
  createdBy: number;

  /**
   * Thời gian tạo dự án (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian tạo dự án (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Thời gian cập nhật dự án (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật dự án (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  updatedAt: number | null;

  /**
   * Trạng thái hoạt động của dự án
   * @example true
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của dự án',
    example: true,
    nullable: true,
  })
  isActive: boolean | null;
}
