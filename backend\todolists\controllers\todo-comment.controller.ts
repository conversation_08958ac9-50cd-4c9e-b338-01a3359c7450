import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { TodoCommentService } from '../services/todo-comment.service';
import { CreateTodoCommentDto } from '../dto/todo-comment/create-todo-comment.dto';
import { CreateSystemEventDto } from '../dto/todo-comment/create-system-event.dto';
import { TodoCommentQueryDto } from '../dto/todo-comment/todo-comment-query.dto';
import { TodoCommentResponseDto } from '../dto/todo-comment/todo-comment-response.dto';

/**
 * Controller xử lý các API liên quan đến bình luận công việc
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(ApiResponseDto, TodoCommentResponseDto)
@Controller('api/v1/todo-comments')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class TodoCommentController {
  constructor(private readonly todoCommentService: TodoCommentService) {}

  /**
   * Tạo bình luận mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo bình luận mới' })
  @ApiResponse({
    status: 201,
    description: 'Bình luận đã được tạo thành công',
    schema: ApiResponseDto.getSchema(TodoCommentResponseDto),
  })
  async createComment(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateTodoCommentDto,
  ): Promise<ApiResponseDto<TodoCommentResponseDto>> {
    const comment = await this.todoCommentService.createComment(
      Number(user.tenantId),
      user.id,
      createDto,
    );
    return ApiResponseDto.created(comment, 'Đã tạo bình luận thành công');
  }

  /**
   * Tạo sự kiện hệ thống
   * Lưu ý: API này chỉ nên được gọi từ các service nội bộ, không nên expose ra bên ngoài
   * Trong thực tế, nên sử dụng EventService trực tiếp từ các service khác
   */
  @Post('system-event')
  @ApiOperation({ summary: 'Tạo sự kiện hệ thống' })
  @ApiResponse({
    status: 201,
    description: 'Sự kiện hệ thống đã được tạo thành công',
    schema: ApiResponseDto.getSchema(TodoCommentResponseDto),
  })
  async createSystemEvent(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateSystemEventDto,
  ): Promise<ApiResponseDto<TodoCommentResponseDto>> {
    // Kiểm tra actorId trong eventData phải trùng với ID người dùng hiện tại
    if (createDto.eventData.actorId !== user.id) {
      throw new AppException(
        TODOLISTS_ERROR_CODES.SYSTEM_EVENT_CREATION_FAILED,
        'Không thể tạo sự kiện hệ thống với actorId khác với người dùng hiện tại',
      );
    }

    const systemEvent = await this.todoCommentService.createSystemEvent(
      Number(user.tenantId),
      createDto,
    );
    return ApiResponseDto.created(
      systemEvent,
      'Đã tạo sự kiện hệ thống thành công',
    );
  }

  /**
   * Lấy danh sách bình luận
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách bình luận' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách bình luận',
    schema: ApiResponseDto.getPaginatedSchema(TodoCommentResponseDto),
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: TodoCommentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TodoCommentResponseDto>>> {
    const paginatedComments = await this.todoCommentService.findAll(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(
      paginatedComments,
      'Lấy danh sách bình luận thành công',
    );
  }

  /**
   * Lấy danh sách bình luận của một công việc
   */
  @Get('by-todo/:todoId')
  @ApiOperation({ summary: 'Lấy danh sách bình luận của một công việc' })
  @ApiParam({ name: 'todoId', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách bình luận của công việc',
    schema: ApiResponseDto.getArraySchema(TodoCommentResponseDto),
  })
  async findByTodoId(
    @CurrentUser() user: JwtPayload,
    @Param('todoId', ParseIntPipe) todoId: number,
  ): Promise<ApiResponseDto<TodoCommentResponseDto[]>> {
    const comments = await this.todoCommentService.findByTodoId(
      Number(user.tenantId),
      todoId,
    );
    return ApiResponseDto.success(
      comments,
      'Lấy danh sách bình luận của công việc thành công',
    );
  }

  /**
   * Lấy chi tiết bình luận
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết bình luận' })
  @ApiParam({ name: 'id', description: 'ID bình luận', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết bình luận',
    schema: ApiResponseDto.getSchema(TodoCommentResponseDto),
  })
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TodoCommentResponseDto>> {
    const comment = await this.todoCommentService.findById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(comment, 'Lấy chi tiết bình luận thành công');
  }

  /**
   * Lấy danh sách trả lời của một bình luận
   */
  @Get(':id/replies')
  @ApiOperation({ summary: 'Lấy danh sách trả lời của một bình luận' })
  @ApiParam({ name: 'id', description: 'ID bình luận cha', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách trả lời của bình luận',
    schema: ApiResponseDto.getArraySchema(TodoCommentResponseDto),
  })
  async findReplies(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TodoCommentResponseDto[]>> {
    const replies = await this.todoCommentService.findReplies(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(replies, 'Lấy danh sách trả lời thành công');
  }

  /**
   * Xóa bình luận
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa bình luận' })
  @ApiParam({ name: 'id', description: 'ID bình luận', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Bình luận đã được xóa thành công',
    schema: ApiResponseDto.getSchema(null),
  })
  async removeComment(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<null>> {
    await this.todoCommentService.removeComment(
      Number(user.tenantId),
      user.id,
      id,
    );
    return ApiResponseDto.success(null, 'Đã xóa bình luận thành công');
  }
}
