## 2.2.2.17 <PERSON><PERSON> tả use case Tạo công việc con

**<PERSON><PERSON> tả ngắn:** Cho phép người dùng tạo công việc con (subtask) cho một công việc đã tồn tại, nhằm chia nhỏ công việc thành các phần dễ quản lý hơn.

### Luồng các sự kiện:

#### Luồng cơ bản:
1) Khi người dùng đang xem chi tiết một công việc, họ chọn "Tạo công việc con".
2) <PERSON>ệ thống hiển thị form tạo công việc con, tự động thiết lập công việc hiện tại là công việc cha.
3) Người dùng nhập các thông tin của công việc con: tiêu đề, mô tả, người đượ<PERSON> giao, độ ưu tiên.
4) <PERSON><PERSON><PERSON><PERSON> dùng nhấn "Tạo công việc con" và hệ thống kiểm tra dữ liệu.
5) <PERSON><PERSON> thống tạo công việc con mới với trạng thái mặc định là "Chờ xử lý" (PENDING), tự động kế thừa categoryId từ công việc cha (nếu có), lưu thời điểm tạo và hiển thị thông báo "Công việc con đã được tạo thành công".
6) Hệ thống cập nhật danh sách công việc con trong giao diện chi tiết công việc cha.
7) Use case kết thúc.

#### Luồng rẽ nhánh:
1) Nếu người dùng không nhập tiêu đề công việc con, hệ thống hiển thị cảnh báo "Vui lòng nhập tiêu đề công việc".
2) Nếu công việc cha không tồn tại (đã bị xóa hoặc ID không hợp lệ), hệ thống hiển thị thông báo "Không tìm thấy công việc cha" và kết thúc use case.
3) Nếu người dùng không có quyền tạo công việc con cho công việc cha (không phải người tạo hoặc người được giao công việc cha), hệ thống hiển thị thông báo "Bạn không có quyền tạo công việc con cho công việc này" và kết thúc use case.
4) Nếu người dùng nhấn "Hủy", hệ thống trở về màn hình chi tiết công việc cha mà không tạo công việc con.
5) Khi có lỗi hệ thống/DB, hiển thị thông báo "Lỗi hệ thống, vui lòng thử lại sau" và kết thúc use case.

### Các yêu cầu đặc biệt:
- Công việc con tự động kế thừa dự án (categoryId) từ công việc cha.
- Trạng thái hoàn thành của công việc cha phụ thuộc vào trạng thái của tất cả công việc con.
- Cho phép tạo nhiều cấp công việc con (công việc con của công việc con).

### Tiền điều kiện:
- Người dùng đã đăng nhập.
- Công việc cha đã tồn tại trong hệ thống.
- Người dùng là người tạo hoặc người được giao công việc cha, hoặc là admin của dự án chứa công việc cha.

### Hậu điều kiện:
- Công việc con mới được tạo với trạng thái "Chờ xử lý" (PENDING).
- Công việc con được liên kết với công việc cha (có parentId trỏ đến ID của công việc cha).
- Công việc con xuất hiện trong danh sách công việc con của công việc cha.

### Điểm mở rộng:
- Tích hợp tính năng sao chép thuộc tính từ công việc cha (như độ ưu tiên, số sao kỳ vọng).
- Cho phép tạo nhanh nhiều công việc con cùng lúc từ template hoặc danh sách có sẵn.
- Bổ sung tính năng phân bổ thời gian và tài nguyên giữa các công việc con dựa trên khối lượng công việc. 