import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '@/modules/auth/auth.module';

// Entities
import { Project } from './entities/project.entity';
import { ProjectMember } from './entities/project-members.entity';
import { Todo } from './entities/todo.entity';
import { TodoComment } from './entities/todo-comment.entity';
import { TodoAttachment } from './entities/todo-attachment.entity';
import { TodoCollaborator } from './entities/todo-collaborator.entity';
import { TodoTag } from './entities/todo-tag.entity';
import { TodoAppreciation } from './entities/todo-appreciation.entity';
import { TodoScore } from './entities/todo-score.entity';
import { TaskKr } from './entities/task-kr.entity';

// Repositories
import {
  ProjectRepository,
  ProjectMemberRepository,
  TodoRepository,
  TaskKrRepository,
  TodoTagRepository,
  TodoScoreRepository,
  TodoCollaboratorRepository,
  TodoAttachmentRepository,
  TodoCommentRepository,
} from './repositories';

// Services
import { ProjectService } from './services/project.service';
import { TodoService } from './services/todo.service';
import { TodoTagService } from './services/todo-tag.service';
import { StatisticsService } from './services/statistics.service';
import { TodoCollaboratorService } from './services/todo-collaborator.service';
import { TodoAttachmentService } from './services/todo-attachment.service';
import { TodoCommentService } from './services/todo-comment.service';
import { EventService } from './services/event.service';

// Controllers
import { ProjectController } from './controllers/project.controller';
import { TodoController } from './controllers/todo.controller';
import { StatisticsController } from './controllers/statistics.controller';
import { TodoCollaboratorController } from './controllers/todo-collaborator.controller';
import { TodoAttachmentController } from './controllers/todo-attachment.controller';
import { TodoCommentController } from './controllers/todo-comment.controller';

/**
 * Module quản lý công việc và dự án
 */
@Global()
@Module({
  imports: [
    AuthModule,
    TypeOrmModule.forFeature([
      Project,
      ProjectMember,
      Todo,
      TodoComment,
      TodoAttachment,
      TodoCollaborator,
      TodoTag,
      TodoAppreciation,
      TodoScore,
      TaskKr,
    ]),
  ],
  controllers: [
    ProjectController,
    TodoController,
    StatisticsController,
    TodoCollaboratorController,
    TodoAttachmentController,
    TodoCommentController,
  ],
  providers: [
    // Repositories
    ProjectRepository,
    ProjectMemberRepository,
    TodoRepository,
    TaskKrRepository,
    TodoTagRepository,
    TodoScoreRepository,
    TodoCollaboratorRepository,
    TodoAttachmentRepository,
    TodoCommentRepository,

    // Services
    ProjectService,
    TodoService,
    TodoTagService,
    StatisticsService,
    TodoCollaboratorService,
    TodoAttachmentService,
    TodoCommentService,
    EventService,
  ],
  exports: [
    ProjectService,
    TodoService,
    TodoTagService,
    StatisticsService,
    TodoCollaboratorService,
    TodoAttachmentService,
    TodoCommentService,
    EventService,
  ],
})
export class TodolistsModule {}
