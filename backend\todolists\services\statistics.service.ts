import { Injectable, Logger } from '@nestjs/common';
import { TodoRepository } from '../repositories/todo.repository';
import { TodoScoreRepository } from '../repositories/todo-score.repository';
import { ProjectRepository } from '../repositories/project.repository';
import { ProjectMemberRepository } from '../repositories/project-member.repository';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { TodoStatus } from '../enum/todo-status.enum';

/**
 * Service xử lý thống kê cho module Todolists
 */
@Injectable()
export class StatisticsService {
  private readonly logger = new Logger(StatisticsService.name);

  constructor(
    private readonly todoRepository: TodoRepository,
    private readonly todoScoreRepository: TodoScoreRepository,
    private readonly projectRepository: ProjectRepository,
    private readonly projectMemberRepository: ProjectMemberRepository,
  ) {}

  /**
   * <PERSON><PERSON>y thống kê hiệu suất của người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng
   * @param startDate Ngày bắt đầu (timestamp)
   * @param endDate Ngày kết thúc (timestamp)
   * @param projectId ID dự án (tùy chọn)
   * @returns Thông tin hiệu suất người dùng
   */
  async getUserPerformance(
    tenantId: number,
    userId: number,
    startDate?: number,
    endDate?: number,
    projectId?: number,
  ) {
    try {
      // Lấy tất cả công việc của người dùng
      const todos = await this.todoRepository.findAll(tenantId, {
        page: 1,
        limit: 1000,
        assigneeId: userId,
      });
      const todoItems = todos.items;

      // Áp dụng bộ lọc thời gian nếu có
      let filteredTodos = [...todoItems];

      if (startDate) {
        filteredTodos = filteredTodos.filter(
          (todo) => todo.createdAt && todo.createdAt >= startDate,
        );
      }

      if (endDate) {
        filteredTodos = filteredTodos.filter(
          (todo) => todo.createdAt && todo.createdAt <= endDate,
        );
      }

      // Áp dụng bộ lọc dự án nếu có
      if (projectId) {
        filteredTodos = filteredTodos.filter(
          (todo) => todo.categoryId === projectId,
        );
      }

      // Tính toán các chỉ số
      const totalTasks = filteredTodos.length;
      const completedTasks = filteredTodos.filter(
        (todo) =>
          todo.status === TodoStatus.COMPLETED ||
          todo.status === TodoStatus.APPROVED,
      ).length;
      const inProgressTasks = filteredTodos.filter(
        (todo) => todo.status === TodoStatus.IN_PROGRESS,
      ).length;
      const pendingTasks = filteredTodos.filter(
        (todo) => todo.status === TodoStatus.PENDING,
      ).length;

      // Tính điểm trung bình
      let totalScore = 0;
      let scoredTasksCount = 0;

      for (const todo of filteredTodos) {
        if (todo.awardedStars) {
          totalScore += todo.awardedStars;
          scoredTasksCount++;
        }
      }

      const averageScore =
        scoredTasksCount > 0 ? totalScore / scoredTasksCount : 0;

      // Tính tỷ lệ hoàn thành
      const completionRate =
        totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      // Trả về kết quả
      return {
        user: {
          id: userId,
          // Lưu ý: Cần tích hợp với module User để lấy thông tin đầy đủ
        },
        totalTasks,
        completedTasks,
        inProgressTasks,
        pendingTasks,
        averageScore: parseFloat(averageScore.toFixed(2)),
        completionRate: parseFloat(completionRate.toFixed(2)),
        statistics: {
          byStatus: {
            completed: completedTasks,
            inProgress: inProgressTasks,
            pending: pendingTasks,
          },
          byScore: {
            averageScore: parseFloat(averageScore.toFixed(2)),
            totalScoredTasks: scoredTasksCount,
          },
        },
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy thống kê hiệu suất người dùng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_STATISTICS_FAILED,
        `Lấy thống kê hiệu suất người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thống kê hiệu suất của dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param startDate Ngày bắt đầu (timestamp)
   * @param endDate Ngày kết thúc (timestamp)
   * @returns Thông tin hiệu suất dự án
   */
  async getProjectPerformance(
    tenantId: number,
    projectId: number,
    startDate?: number,
    endDate?: number,
  ) {
    try {
      // Kiểm tra dự án tồn tại
      const project = await this.projectRepository.findById(
        tenantId,
        projectId,
      );
      if (!project) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.PROJECT_NOT_FOUND,
          `Không tìm thấy dự án với ID ${projectId}`,
        );
      }

      // Lấy tất cả công việc của dự án
      const todos = await this.todoRepository.findAll(tenantId, {
        page: 1,
        limit: 1000,
        categoryId: projectId,
      });
      const todoItems = todos.items;

      // Áp dụng bộ lọc thời gian nếu có
      let filteredTodos = [...todoItems];

      if (startDate) {
        filteredTodos = filteredTodos.filter(
          (todo) => todo.createdAt && todo.createdAt >= startDate,
        );
      }

      if (endDate) {
        filteredTodos = filteredTodos.filter(
          (todo) => todo.createdAt && todo.createdAt <= endDate,
        );
      }

      // Tính toán các chỉ số
      const totalTasks = filteredTodos.length;
      const completedTasks = filteredTodos.filter(
        (todo) =>
          todo.status === TodoStatus.COMPLETED ||
          todo.status === TodoStatus.APPROVED,
      ).length;
      const inProgressTasks = filteredTodos.filter(
        (todo) => todo.status === TodoStatus.IN_PROGRESS,
      ).length;
      const pendingTasks = filteredTodos.filter(
        (todo) => todo.status === TodoStatus.PENDING,
      ).length;

      // Tính điểm trung bình
      let totalScore = 0;
      let scoredTasksCount = 0;

      for (const todo of filteredTodos) {
        if (todo.awardedStars) {
          totalScore += todo.awardedStars;
          scoredTasksCount++;
        }
      }

      const averageScore =
        scoredTasksCount > 0 ? totalScore / scoredTasksCount : 0;

      // Tính tỷ lệ hoàn thành
      const completionRate =
        totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      // Lấy danh sách thành viên dự án
      const projectMembersResult =
        await this.projectMemberRepository.findAllByProjectId(
          tenantId,
          projectId,
          {
            page: 1,
            limit: 100,
          },
        );
      const projectMembers = projectMembersResult.items;

      // Tính toán hiệu suất của từng thành viên
      const memberPerformance: Array<{
        userId: number;
        role: string;
        totalTasks: number;
        completedTasks: number;
        averageScore: number;
        completionRate: number;
      }> = [];

      for (const member of projectMembers || []) {
        const memberTodos = filteredTodos.filter(
          (todo) => todo.assigneeId === member.userId,
        );

        if (memberTodos.length > 0) {
          const memberCompletedTasks = memberTodos.filter(
            (todo) =>
              todo.status === TodoStatus.COMPLETED ||
              todo.status === TodoStatus.APPROVED,
          ).length;

          let memberTotalScore = 0;
          let memberScoredTasksCount = 0;

          for (const todo of memberTodos) {
            if (todo.awardedStars) {
              memberTotalScore += todo.awardedStars;
              memberScoredTasksCount++;
            }
          }

          const memberAverageScore =
            memberScoredTasksCount > 0
              ? memberTotalScore / memberScoredTasksCount
              : 0;

          const memberCompletionRate =
            memberTodos.length > 0
              ? (memberCompletedTasks / memberTodos.length) * 100
              : 0;

          if (member.userId) {
            memberPerformance.push({
              userId: member.userId,
              role: member.role,
              totalTasks: memberTodos.length,
              completedTasks: memberCompletedTasks,
              averageScore: parseFloat(memberAverageScore.toFixed(2)),
              completionRate: parseFloat(memberCompletionRate.toFixed(2)),
            });
          }
        }
      }

      // Trả về kết quả
      return {
        project: {
          id: projectId,
          name: project.title,
          description: project.description,
          isActive: project.isActive || false,
        },
        totalTasks,
        completedTasks,
        inProgressTasks,
        pendingTasks,
        averageScore: parseFloat(averageScore.toFixed(2)),
        completionRate: parseFloat(completionRate.toFixed(2)),
        statistics: {
          byStatus: {
            completed: completedTasks,
            inProgress: inProgressTasks,
            pending: pendingTasks,
          },
          byScore: {
            averageScore: parseFloat(averageScore.toFixed(2)),
            totalScoredTasks: scoredTasksCount,
          },
        },
        members: memberPerformance,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy thống kê hiệu suất dự án: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_STATISTICS_FAILED,
        `Lấy thống kê hiệu suất dự án thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy dữ liệu biểu đồ Gantt cho dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param startDate Ngày bắt đầu (timestamp)
   * @param endDate Ngày kết thúc (timestamp)
   * @returns Dữ liệu biểu đồ Gantt
   */
  async getProjectGanttChart(
    tenantId: number,
    projectId: number,
    startDate?: number,
    endDate?: number,
  ) {
    try {
      // Kiểm tra dự án tồn tại
      const project = await this.projectRepository.findById(
        tenantId,
        projectId,
      );
      if (!project) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.PROJECT_NOT_FOUND,
          `Không tìm thấy dự án với ID ${projectId}`,
        );
      }

      // Lấy tất cả công việc của dự án
      const todos = await this.todoRepository.findAll(tenantId, {
        page: 1,
        limit: 1000,
        categoryId: projectId,
      });
      const todoItems = todos.items;

      // Áp dụng bộ lọc thời gian nếu có
      let filteredTodos = [...todoItems];

      if (startDate) {
        filteredTodos = filteredTodos.filter(
          (todo) => todo.createdAt && todo.createdAt >= startDate,
        );
      }

      if (endDate) {
        filteredTodos = filteredTodos.filter(
          (todo) => todo.createdAt && todo.createdAt <= endDate,
        );
      }

      // Lấy danh sách thành viên dự án
      const projectMembersResult =
        await this.projectMemberRepository.findAllByProjectId(
          tenantId,
          projectId,
          {
            page: 1,
            limit: 100,
          },
        );
      const projectMembers = projectMembersResult.items;

      // Chuyển đổi thành định dạng dữ liệu biểu đồ Gantt
      const ganttTasks = filteredTodos.map((todo) => {
        // Tìm thông tin người được giao
        const assignee = projectMembers.find(
          (member) => member.userId === todo.assigneeId,
        );

        // Tính toán tiến độ
        let progress = 0;
        if (
          todo.status === TodoStatus.COMPLETED ||
          todo.status === TodoStatus.APPROVED
        ) {
          progress = 100;
        } else if (todo.status === TodoStatus.IN_PROGRESS) {
          progress = 50; // Giả định 50% cho công việc đang thực hiện
        }

        // Tính toán thời gian bắt đầu và kết thúc
        const startTime = todo.createdAt || Date.now();
        const endTime = todo.completedAt || startTime + 7 * 24 * 60 * 60 * 1000; // Mặc định 1 tuần nếu không có thời gian hoàn thành

        return {
          id: todo.id,
          title: todo.title,
          start: startTime,
          end: endTime,
          progress: progress,
          status: todo.status,
          assigneeId: todo.assigneeId,
          assigneeName: assignee ? `User ${assignee.userId}` : 'Unassigned', // Cần tích hợp với module User để lấy tên
          dependencies: todo.parentId ? [todo.parentId] : [],
        };
      });

      // Sắp xếp công việc theo thời gian bắt đầu
      ganttTasks.sort((a, b) => a.start - b.start);

      // Trả về kết quả
      return {
        project: {
          id: projectId,
          name: project.title,
          description: project.description,
          isActive: project.isActive || false,
        },
        timeRange: {
          start:
            ganttTasks.length > 0
              ? Math.min(...ganttTasks.map((task) => task.start))
              : startDate || Date.now(),
          end:
            ganttTasks.length > 0
              ? Math.max(...ganttTasks.map((task) => task.end))
              : endDate || Date.now() + 30 * 24 * 60 * 60 * 1000,
        },
        tasks: ganttTasks,
        members: projectMembers
          .filter((member) => member.userId)
          .map((member) => ({
            userId: member.userId as number,
            role: member.role,
          })),
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy dữ liệu biểu đồ Gantt: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_STATISTICS_FAILED,
        `Lấy dữ liệu biểu đồ Gantt thất bại: ${error.message}`,
      );
    }
  }
}
