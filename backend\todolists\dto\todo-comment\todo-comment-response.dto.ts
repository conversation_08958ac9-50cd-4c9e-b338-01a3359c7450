import { ApiProperty } from '@nestjs/swagger';
import { CommentType } from '../../enum/comment-type.enum';
import { SystemEventData } from '../../interfaces/system-event-data.type';
import { ResourceDto } from './resource.dto';
import { MentionDto } from './mention.dto';

/**
 * DTO cho phản hồi thông tin bình luận của công việc
 */
export class TodoCommentResponseDto {
  /**
   * ID của bình luận
   * @example 1
   */
  @ApiProperty({
    description: 'ID của bình luận',
    example: 1,
  })
  id: number;

  /**
   * ID của công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
    nullable: true,
  })
  todoId: number | null;

  /**
   * ID của người dùng tạo bình luận
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người dùng tạo bình luận',
    example: 1,
    nullable: true,
  })
  userId: number | null;

  /**
   * Nội dung bình luận dạng HTML
   * @example "<p>Đây là bình luận với <strong>định dạng</strong></p>"
   */
  @ApiProperty({
    description: 'Nội dung bình luận dạng HTML',
    example: '<p>Đây là bình luận với <strong>định dạng</strong></p>',
  })
  contentHtml: string;

  /**
   * Thời gian tạo (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * ID của bình luận cha (nếu đây là trả lời)
   * @example 1
   */
  @ApiProperty({
    description: 'ID của bình luận cha (nếu đây là trả lời)',
    example: 1,
    nullable: true,
  })
  parentId: number | null;

  /**
   * Loại bình luận
   * @example "note"
   */
  @ApiProperty({
    description: 'Loại bình luận',
    enum: CommentType,
    example: CommentType.NOTE,
    nullable: true,
  })
  commentType: CommentType | null;

  /**
   * Xác định đây là bình luận người dùng (false) hay sự kiện hệ thống (true)
   * @example false
   */
  @ApiProperty({
    description:
      'Xác định đây là bình luận người dùng (false) hay sự kiện hệ thống (true)',
    example: false,
  })
  isSystemEvent: boolean;

  /**
   * Dữ liệu chi tiết của sự kiện hệ thống (JSON)
   * @example { "eventType": "status_changed", "oldValue": "pending", "newValue": "in_progress", "actorId": 1, "changedAt": 1625097600000 }
   */
  @ApiProperty({
    description: 'Dữ liệu chi tiết của sự kiện hệ thống (JSON)',
    example: {
      eventType: 'status_changed',
      oldValue: 'pending',
      newValue: 'in_progress',
      actorId: 1,
      changedAt: 1625097600000,
    },
    nullable: true,
  })
  eventData: SystemEventData | null;

  /**
   * Danh sách tài nguyên (ảnh, file) liên quan
   * @example [{ "type": "image", "url": "https://example.com/photo.jpg", "name": "photo.jpg" }]
   */
  @ApiProperty({
    description: 'Danh sách tài nguyên (ảnh, file) liên quan',
    example: [
      {
        type: 'image',
        url: 'https://example.com/photo.jpg',
        name: 'photo.jpg',
      },
    ],
    nullable: true,
    type: [ResourceDto],
  })
  resources: ResourceDto[] | null;

  /**
   * Danh sách người dùng được mention trong bình luận
   * @example [{ "userId": 123, "username": "userA" }]
   */
  @ApiProperty({
    description: 'Danh sách người dùng được mention trong bình luận',
    example: [{ userId: 123, username: 'userA' }],
    nullable: true,
    type: [MentionDto],
  })
  mentions: MentionDto[] | null;
}
