import React from 'react';
import { cn } from '@/shared/utils/cn';

interface StarRatingProps {
  /**
   * Current rating value (1-5)
   */
  value: number;
  
  /**
   * Maximum number of stars
   */
  maxStars?: number;
  
  /**
   * Whether the rating is interactive
   */
  interactive?: boolean;
  
  /**
   * Size of the stars
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Callback when rating changes
   */
  onChange?: (rating: number) => void;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Star Rating Component
 */
const StarRating: React.FC<StarRatingProps> = ({
  value,
  maxStars = 5,
  interactive = false,
  size = 'md',
  onChange,
  className = '',
}) => {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const handleStarClick = (rating: number) => {
    if (interactive && onChange) {
      onChange(rating);
    }
  };

  return (
    <div className={cn('flex', className)}>
      {Array.from({ length: maxStars }).map((_, index) => {
        const starValue = index + 1;
        const isFilled = starValue <= value;
        
        return (
          <span
            key={index}
            className={cn(
              sizeClasses[size],
              isFilled ? 'text-yellow-400' : 'text-gray-300',
              interactive && 'cursor-pointer hover:text-yellow-300 transition-colors'
            )}
            onClick={() => handleStarClick(starValue)}
          >
            ★
          </span>
        );
      })}
    </div>
  );
};

export default StarRating;
