import React from 'react';
import { useTranslation } from 'react-i18next';

import { Typography, Divider } from '@/shared/components/common';
import { formatTimestamp } from '@/shared/utils/form-date-utils';

import { TaskDto } from '../../types/task.types';

interface TaskDetailInfoProps {
  task: TaskDto;
}

/**
 * Task detail information component
 */
const TaskDetailInfo: React.FC<TaskDetailInfoProps> = ({ task }) => {
  const { t } = useTranslation(['todolist']);

  return (
    <div className="space-y-4">
      <div>
        <Typography variant="h6" className="mb-2">
          {t('todolist:task.fields.description', 'Description')}
        </Typography>
        <Typography variant="body1">
          {task.description || t('todolist:task.noDescription', 'No description provided')}
        </Typography>
      </div>

      <Divider />

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Typography variant="subtitle2" className="text-gray-500">
            {t('todolist:task.fields.createdAt', 'Created At')}
          </Typography>
          <Typography variant="body2">{formatTimestamp(task.createdAt || 0)}</Typography>
        </div>
        <div>
          <Typography variant="subtitle2" className="text-gray-500">
            {t('todolist:task.fields.updatedAt', 'Updated At')}
          </Typography>
          <Typography variant="body2">{formatTimestamp(task.updatedAt || 0)}</Typography>
        </div>
        {task.completedAt && (
          <div>
            <Typography variant="subtitle2" className="text-gray-500">
              {t('todolist:task.fields.completedAt', 'Completed At')}
            </Typography>
            <Typography variant="body2">{formatTimestamp(task.completedAt)}</Typography>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskDetailInfo;
