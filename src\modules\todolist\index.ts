// Export types
export * from './types/task.types';
export * from './types/project.types';
export * from './types/comment.types';
export * from './types/attachment.types';
export * from './types/statistics.types';
export * from './types/okr-integration.types';

// Export services
export * from './services/task.service';
export * from './services/project.service';
export * from './services/comment.service';
export * from './services/attachment.service';
export * from './services/statistics.service';
export * from './services/okr-integration.service';

// Export hooks
export * from './hooks/useTasks';
export * from './hooks/useProjects';
export * from './hooks/useComments';
export * from './hooks/useAttachments';
export * from './hooks/useStatistics';
export * from './hooks/useOkrIntegration';
export * from './hooks/useOptimizedQuery';
export * from './hooks/useVirtualizedList';

// Export components
export { default as TaskForm } from './components/TaskForm';
export { default as ProjectForm } from './components/ProjectForm';
export { default as ProjectMemberForm } from './components/ProjectMemberForm';
export { default as RecentTasksList } from './components/RecentTasksList';
export { default as SubtaskList } from './components/SubtaskList';
export { default as VirtualizedTaskList } from './components/VirtualizedTaskList';

// Export task detail components
export { default as CommentsSection } from './components/task-detail/CommentsSection';
export { default as TodoCommentsSection } from './components/task-detail/TodoCommentsSection';
export { default as SubtasksSection } from './components/task-detail/SubtasksSection';

// Export pages
export { default as TodolistHomePage } from './pages/TodolistHomePage';
export { default as TaskListPage } from './pages/TaskListPage';
export { default as TaskDetailPage } from './pages/TaskDetailPage';
export { default as ProjectListPage } from './pages/ProjectListPage';
export { default as ProjectDetailPage } from './pages/ProjectDetailPage';
export { default as DashboardPage } from './pages/DashboardPage';
export { default as StatisticsPage } from './pages/StatisticsPage';
export { default as StatisticsPageEnhanced } from './pages/StatisticsPageEnhanced';
export { default as EmployeeGanttPage } from './pages/EmployeeGanttPage';
export { default as EmployeeGanttPageSimple } from './pages/EmployeeGanttPageSimple';

// Export routers
export * from './routers/todolistRoutes';

// Export locales
export * from './locales';
