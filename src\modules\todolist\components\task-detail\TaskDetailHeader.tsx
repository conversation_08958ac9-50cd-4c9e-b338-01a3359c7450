import React from 'react';
import { useTranslation } from 'react-i18next';

import { IconCard, Typography } from '@/shared/components/common';

interface TaskDetailHeaderProps {
  title: string;
  onBack: () => void;
  onReload: () => void;
  onMarkComplete: () => void;
  isCompleted: boolean;
}

/**
 * Header component for task detail page
 */
const TaskDetailHeader: React.FC<TaskDetailHeaderProps> = ({
  title,
  onBack,
  onReload,
  onMarkComplete,
  isCompleted,
}) => {
  const { t } = useTranslation(['common', 'todolist']);

  return (
    <div className="flex justify-between items-center">
      <Typography variant="h4">{title}</Typography>
      <div className="flex space-x-2">
        <IconCard icon="arrow-left" onClick={onBack} title={t('common:back', 'Back')} />
        <IconCard icon="refresh-cw" onClick={onReload} title={t('common:refresh', 'Refresh')} />
        <IconCard
          icon={isCompleted ? 'check-circle' : 'circle'}
          onClick={onMarkComplete}
          title={
            isCompleted
              ? t('todolist:task.markIncomplete', 'Mark as incomplete')
              : t('todolist:task.markComplete', 'Mark as complete')
          }
          className={isCompleted ? 'text-green-600' : 'text-gray-400'}
        />
      </div>
    </div>
  );
};

export default TaskDetailHeader;
