import React from 'react';
import { useTranslation } from 'react-i18next';

import { 
  Typography, 
  ScrollArea,
  Button
} from '@/shared/components/common';
import { formatTimestamp } from '@/shared/utils/form-date-utils';

import { TaskDto } from '../../types/task.types';

interface SubtasksSectionProps {
  taskId: number;
  subtasks: TaskDto[];
  isLoading: boolean;
  onAddSubtask: () => void;
  onRefresh: () => void;
}

/**
 * Subtasks section component
 */
const SubtasksSection: React.FC<SubtasksSectionProps> = ({
  taskId,
  subtasks,
  isLoading,
  onAddSubtask,
  onRefresh,
}) => {
  const { t } = useTranslation(['todolist', 'common']);

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <Typography variant="h6">
            {t('todolist:task.subtasks.title', 'Subtasks')}
          </Typography>
          <Button size="sm" onClick={onAddSubtask}>
            {t('todolist:task.subtasks.add', 'Add Subtask')}
          </Button>
        </div>
      </div>

      {/* Subtasks list */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full p-4">
          {subtasks.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-8">
              <div className="text-4xl mb-4">📝</div>
              <Typography variant="h6" className="mb-2">
                {t('todolist:task.subtasks.empty', 'No subtasks yet')}
              </Typography>
              <Typography variant="body2" className="text-gray-500">
                {t('todolist:task.subtasks.emptyDescription', 'Break down this task into smaller steps')}
              </Typography>
            </div>
          ) : (
            <div className="space-y-3">
              {subtasks.map((subtask) => (
                <div key={subtask.id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <Typography variant="subtitle2" className="font-medium mb-1">
                        {subtask.title}
                      </Typography>
                      {subtask.description && (
                        <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-2">
                          {subtask.description}
                        </Typography>
                      )}
                      <div className="flex items-center space-x-2">
                        <Typography variant="caption" className="text-gray-500">
                          {formatTimestamp(subtask.createdAt || 0)}
                        </Typography>
                        {subtask.status && (
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            subtask.status === 'completed' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {subtask.status}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
};

export default SubtasksSection;
