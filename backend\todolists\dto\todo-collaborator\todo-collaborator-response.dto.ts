import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin cộng tác viên của công việc
 */
export class TodoCollaboratorResponseDto {
  /**
   * ID của bản ghi cộng tác viên
   * @example 1
   */
  @ApiProperty({
    description: 'ID của bản ghi cộng tác viên',
    example: 1,
  })
  id: number;

  /**
   * ID của công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
    nullable: true,
  })
  todoId: number | null;

  /**
   * ID của người dùng là cộng tác viên
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người dùng là cộng tác viên',
    example: 1,
    nullable: true,
  })
  userId: number | null;

  /**
   * Thời gian tạo (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thờ<PERSON> gian tạo (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;
}
