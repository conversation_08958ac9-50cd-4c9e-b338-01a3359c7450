import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Todo } from '../entities/todo.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { TodoQueryDto } from '../dto/todo/todo-query.dto';
import { TodoStatus } from '../enum/todo-status.enum';

/**
 * Repository cho entity Todo
 */
@Injectable()
export class TodoRepository {
  constructor(
    @InjectRepository(Todo)
    private readonly repository: Repository<Todo>,
  ) {}

  /**
   * Tạo công việc mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu công việc
   * @returns Công việc đã tạo
   */
  async create(tenantId: number, data: Partial<Todo>): Promise<Todo> {
    const todo = this.repository.create({ ...data, tenantId });
    return this.repository.save(todo);
  }

  /**
   * Tìm tất cả công việc với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách công việc đã phân trang
   */
  async findAll(
    tenantId: number,
    query: TodoQueryDto,
  ): Promise<PaginatedResult<Todo>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
      priority,
      assigneeId,
      categoryId,
      parentId,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('todo');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('todo.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc trạng thái nếu được cung cấp
    if (status) {
      queryBuilder.andWhere('todo.status = :status', { status });
    }

    // Áp dụng bộ lọc mức độ ưu tiên nếu được cung cấp
    if (priority) {
      queryBuilder.andWhere('todo.priority = :priority', { priority });
    }

    // Áp dụng bộ lọc người được giao nếu được cung cấp
    if (assigneeId) {
      queryBuilder.andWhere('todo.assigneeId = :assigneeId', { assigneeId });
    }

    // Áp dụng bộ lọc dự án nếu được cung cấp
    if (categoryId) {
      queryBuilder.andWhere('todo.categoryId = :categoryId', { categoryId });
    }

    // Áp dụng bộ lọc công việc cha nếu được cung cấp
    if (parentId !== undefined) {
      if (parentId === null) {
        queryBuilder.andWhere('todo.parentId IS NULL');
      } else {
        queryBuilder.andWhere('todo.parentId = :parentId', { parentId });
      }
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        'todo.title ILIKE :search OR todo.description ILIKE :search',
        {
          search: `%${search}%`,
        },
      );
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`todo.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm công việc theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @returns Công việc hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<Todo | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm công việc con của một công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param parentId ID công việc cha
   * @param query Tham số truy vấn
   * @returns Danh sách công việc con đã phân trang
   */
  async findSubtasks(
    tenantId: number,
    parentId: number,
    query: TodoQueryDto,
  ): Promise<PaginatedResult<Todo>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('todo');
    queryBuilder.where(
      'todo.parentId = :parentId AND todo.tenantId = :tenantId',
      { parentId, tenantId },
    );

    // Áp dụng bộ lọc trạng thái nếu được cung cấp
    if (status) {
      queryBuilder.andWhere('todo.status = :status', { status });
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        'todo.title ILIKE :search OR todo.description ILIKE :search',
        {
          search: `%${search}%`,
        },
      );
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`todo.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param data Dữ liệu cập nhật
   * @returns Công việc đã cập nhật
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<Todo>,
  ): Promise<Todo | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Cập nhật trạng thái công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param status Trạng thái mới
   * @returns Công việc đã cập nhật
   */
  async updateStatus(
    tenantId: number,
    id: number,
    status: TodoStatus,
  ): Promise<Todo | null> {
    const now = Date.now();
    const data: Partial<Todo> = {
      status,
      updatedAt: now,
    };

    // Nếu trạng thái là COMPLETED, cập nhật completedAt
    if (status === TodoStatus.COMPLETED) {
      data.completedAt = now;
    }

    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Cập nhật điểm đánh giá cho công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param awardedStars Số sao đánh giá (1-5)
   * @returns Công việc đã cập nhật
   */
  async updateScore(
    tenantId: number,
    id: number,
    awardedStars: number,
  ): Promise<Todo | null> {
    const now = Date.now();
    await this.repository.update(
      { id, tenantId },
      {
        awardedStars,
        updatedAt: now,
        status: TodoStatus.APPROVED, // Cập nhật trạng thái thành APPROVED sau khi chấm điểm
      },
    );
    return this.findById(tenantId, id);
  }

  /**
   * Xóa công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @returns Kết quả xóa
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }
}
