import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';
import { CommentType } from '../../enum/comment-type.enum';
import { ResourceDto } from './resource.dto';
import { MentionDto } from './mention.dto';

/**
 * DTO cho tạo mới bình luận cho công việc
 */
export class CreateTodoCommentDto {
  /**
   * ID của công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID công việc không được để trống' })
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId: number;

  /**
   * Nội dung bình luận dạng HTML
   * @example "<p>Đ<PERSON><PERSON> là bình luận với <strong>định dạng</strong></p>"
   */
  @ApiProperty({
    description: 'Nội dung bình luận dạng HTML',
    example: '<p>Đây là bình luận với <strong>định dạng</strong></p>',
    required: true,
  })
  @IsNotEmpty({ message: 'Nội dung bình luận không được để trống' })
  @IsString({ message: 'Nội dung bình luận phải là chuỗi' })
  contentHtml: string;

  /**
   * ID của bình luận cha (nếu đây là trả lời)
   * @example 1
   */
  @ApiProperty({
    description: 'ID của bình luận cha (nếu đây là trả lời)',
    example: 1,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsInt({ message: 'ID bình luận cha phải là số nguyên' })
  @Min(1, { message: 'ID bình luận cha phải lớn hơn 0' })
  parentId?: number;

  /**
   * Loại bình luận
   * @example "note"
   */
  @ApiProperty({
    description: 'Loại bình luận',
    enum: CommentType,
    example: CommentType.NOTE,
    required: false,
    default: CommentType.NOTE,
  })
  @IsOptional()
  @IsEnum(CommentType, { message: 'Loại bình luận không hợp lệ' })
  commentType?: CommentType = CommentType.NOTE;

  /**
   * Danh sách tài nguyên (ảnh, file) liên quan
   * @example [{ "type": "image", "url": "https://example.com/photo.jpg", "name": "photo.jpg" }]
   */
  @ApiProperty({
    description: 'Danh sách tài nguyên (ảnh, file) liên quan',
    example: [
      {
        type: 'image',
        url: 'https://example.com/photo.jpg',
        name: 'photo.jpg',
      },
    ],
    required: false,
    nullable: true,
    type: [ResourceDto],
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ResourceDto)
  resources?: ResourceDto[];

  /**
   * Danh sách người dùng được mention trong bình luận
   * @example [{ "userId": 123, "username": "userA" }]
   */
  @ApiProperty({
    description: 'Danh sách người dùng được mention trong bình luận',
    example: [{ userId: 123, username: 'userA' }],
    required: false,
    nullable: true,
    type: [MentionDto],
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => MentionDto)
  mentions?: MentionDto[];
}
